package ui

import (
	"net/http"

	signalsd "github.com/information-sharing-networks/signalsd/app/internal/server/config"
)

// RequireAuth is middleware that checks authentication and attempts token refresh if needed
func (s *Server) RequireAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		status := s.authService.CheckTokenStatus(r)

		switch status {
		case TokenValid:
			next.ServeHTTP(w, r)
			return
		case TokenMissing, TokenInvalid:
			s.logger.Debug().Msg("Access token is missing or invalid")
			s.redirectToLogin(w, r)
			return
		case TokenExpired:
			// attempt refresh
			s.logger.Debug().Msg("Access token is expired - attempting refresh")
			refreshTokenCookie, err := r.<PERSON>(signalsd.RefreshTokenCookieName)
			if err != nil {
				s.redirectToLogin(w, r)
				return
			}

			s.logger.Debug().Msg("Refresh token found - attempting refresh")
			// Need to get the access token cookie for refresh
			accessTokenCookie, err := r.<PERSON>(accessTokenCookieName)
			if err != nil {
				s.redirectToLogin(w, r)
				return
			}

			s.logger.Debug().Msg("Access token found - attempting refresh")

			// Attempt token refresh (forwarding refresh token cookie to API)
			loginResp, newRefreshTokenCookie, err := s.authService.RefreshToken(accessTokenCookie, refreshTokenCookie)
			if err != nil {
				s.logger.Error().Err(err).Msg("Token refresh failed")
				s.redirectToLogin(w, r)
				return
			}

			s.logger.Debug().Msg("Token refresh successful")

			// Set all authentication cookies using shared method (includes updated permissions)
			if err := s.authService.SetAuthCookies(w, loginResp, newRefreshTokenCookie, s.config.Environment); err != nil {
				s.logger.Error().Err(err).Msg("Failed to set authentication cookies after refresh")
				s.redirectToLogin(w, r)
				return
			}

			s.logger.Info().Msg("Access token refreshed successfully")
			next.ServeHTTP(w, r) // Continue with refreshed token
		}
	})
}
