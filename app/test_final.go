package main

import (
	"github.com/information-sharing-networks/signalsd/app/internal/logger"
	"github.com/information-sharing-networks/signalsd/app/internal/ui"
	"github.com/rs/zerolog"
)

func main() {
	// Initialize base logger (no component)
	baseLogger := logger.InitServerLogger()

	// Create server logger with component=server
	serverLogger := baseLogger.With().Str("component", "server").Logger()

	// Create UI logger with component=ui (simulating what UI server does)
	uiLogger := baseLogger.With().Str("component", "ui").Logger()

	// Demonstrate the logging hierarchy
	baseLogger.Info().Msg("Application starting (base logger)")
	serverLogger.Info().Msg("Server component initialized")
	uiLogger.Info().Msg("UI component initialized")
	authLogger := uiLogger.With().Str("handler", "auth").Logger()
	authLogger.Info().Msg("Auth handler ready")

	// Test UI server creation
	uiConfig := &ui.UIConfig{
		Environment: "dev",
		Host:        "localhost",
		Port:        3000,
		LogLevel:    zerolog.DebugLevel,
		APIBaseURL:  "http://localhost:8080",
	}

	// This should create a UI server with component=ui logger
	_ = ui.NewServer(uiConfig, baseLogger)

	baseLogger.Info().Msg("All components initialized successfully")
}
