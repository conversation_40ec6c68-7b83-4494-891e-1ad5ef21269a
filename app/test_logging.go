package main

import (
	"os"

	"github.com/information-sharing-networks/signalsd/app/internal/logger"
	"github.com/information-sharing-networks/signalsd/app/internal/ui"
	"github.com/rs/zerolog"
)

func main() {
	// Initialize base logger
	baseLogger := logger.InitServerLogger()
	
	// Create server logger with component
	serverLogger := baseLogger.With().Str("component", "server").Logger()
	
	// Create UI config and server to demonstrate UI logging
	uiConfig := &ui.UIConfig{
		Environment: "dev",
		Host:        "localhost",
		Port:        3000,
		LogLevel:    zerolog.DebugLevel,
		APIBaseURL:  "http://localhost:8080",
	}
	
	// Create UI server (this will create its own component logger)
	uiServer := ui.NewServer(uiConfig, baseLogger)
	
	// Demonstrate different log messages
	baseLogger.Info().Msg("Base logger message")
	serverLogger.Info().Msg("Server component message")
	
	// Simulate UI logging
	uiServer.Logger().Info().Msg("UI component message")
	uiServer.Logger().With().Str("handler", "login").Logger().Info().Msg("UI login handler message")
	
	// Simulate different request types
	httpLogger := logger.InitHttpLogger(zerolog.DebugLevel, "dev")
	
	// Simulate API request
	apiLogger := httpLogger.With().
		Str("request_id", "req-123").
		Str("method", "POST").
		Str("path", "/api/signals").
		Str("request_type", "api").
		Logger()
	apiLogger.Info().Int("status", 200).Msg("Request completed")
	
	// Simulate UI request
	uiReqLogger := httpLogger.With().
		Str("request_id", "req-124").
		Str("method", "GET").
		Str("path", "/dashboard").
		Str("request_type", "ui").
		Logger()
	uiReqLogger.Info().Int("status", 200).Msg("Request completed")
}
